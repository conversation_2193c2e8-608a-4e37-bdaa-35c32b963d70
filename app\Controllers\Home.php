<?php

namespace App\Controllers;

use App\Models\districtModel;
use App\Models\employeesModel;
use App\Models\orgModel;
use App\Models\PermissionsUserDistrictsModel;
use App\Models\projectsModel;
use App\Models\provinceModel;
use App\Models\usersModel;
use App\Models\PositionsModel;

class Home extends BaseController
{
    public $session;
    public $usersModel;
    public $orgModel;
    public $provinceModel;
    public $districtModel;
    public $applicantsModel;
    protected $positionsModel;
    public $exerciseModel;




    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();

        $this->usersModel = new usersModel();
        $this->orgModel = new orgModel();
        $this->provinceModel = new provinceModel();
        $this->districtModel = new districtModel();
        $this->applicantsModel = new \App\Models\applicantsModel();
        $this->positionsModel = new PositionsModel();
        $this->exerciseModel = new \App\Models\ExerciseModel();
    }

    public function index()
    {
        // Get random published positions directly linked to exercises
        $positions = $this->positionsModel->getRandomPublishedPositions(10);

        return view('home/home_home', [
            'latest_positions' => $positions
        ]);
    }

    /**
     * Display all published job positions with dummy data based on actual model fields
     */
    public function jobs()
    {
        // Dummy positions data based on PositionsModel fields and joined data from related tables
        $positions = [
            [
                // From positions table (PositionsModel)
                'id' => 1,
                'exercise_id' => 1,
                'org_id' => 1,
                'position_group_id' => 1,
                'position_reference' => 'GOV/IT/2024/001',
                'designation' => 'Senior Software Developer',
                'classification' => 'Professional Officer',
                'award' => 'PNG-7',
                'location' => 'Port Moresby, National Capital District',
                'annual_salary' => 'K85,000 - K95,000',
                'qualifications' => 'Bachelor\'s degree in Computer Science or related field',
                'knowledge' => 'Software development, database management, web technologies',
                'skills_competencies' => 'PHP, JavaScript, MySQL, CodeIgniter, Git',
                'job_experiences' => '5+ years in software development',
                'jd_filepath' => '/uploads/jd/senior_developer_2024.pdf',
                'jd_texts_extracted' => 'Senior Software Developer position for digital transformation...',
                'remarks' => 'Priority position for digital transformation',
                'status' => 1,
                'status_at' => '2024-01-15 10:30:00',
                'status_by' => 1,
                'status_remarks' => 'Approved for publication',
                'created_by' => 1,
                'updated_by' => 1,
                'created_at' => '2024-01-01 09:00:00',
                'updated_at' => '2024-01-15 10:30:00',
                // From dakoii_org table (joined)
                'org_name' => 'Department of Information and Communication Technology',
                'orgcode' => 'DICT',
                // From positions_groups table (joined)
                'group_name' => 'IT Development Group',
                // From exercises table (joined)
                'exercise_name' => 'Government ICT Recruitment Exercise 2024',
                'advertisement_no' => 'ADV/2024/001',
                'publish_date_from' => '2024-01-01',
                'publish_date_to' => '2024-12-31'
            ],
            [
                'id' => 2,
                'exercise_id' => 2,
                'org_id' => 2,
                'position_group_id' => 2,
                'position_reference' => 'GOV/WORKS/2024/002',
                'designation' => 'Project Manager - Infrastructure',
                'classification' => 'Management Officer',
                'award' => 'PNG-8',
                'location' => 'Lae, Morobe Province',
                'annual_salary' => 'K75,000 - K85,000',
                'qualifications' => 'Bachelor\'s degree in Engineering or Project Management',
                'knowledge' => 'Project management, infrastructure development, stakeholder management',
                'skills_competencies' => 'PMP certification, MS Project, Risk Management',
                'job_experiences' => '7+ years in project management',
                'jd_filepath' => '/uploads/jd/project_manager_2024.pdf',
                'jd_texts_extracted' => 'Project Manager Infrastructure position for development projects...',
                'remarks' => 'Critical for infrastructure development',
                'status' => 1,
                'status_at' => '2024-01-20 14:15:00',
                'status_by' => 2,
                'status_remarks' => 'Approved for urgent recruitment',
                'created_by' => 2,
                'updated_by' => 2,
                'created_at' => '2024-01-05 11:00:00',
                'updated_at' => '2024-01-20 14:15:00',
                'org_name' => 'Department of Works and Implementation',
                'orgcode' => 'DWI',
                'group_name' => 'Infrastructure Management',
                'exercise_name' => 'Infrastructure Development Recruitment 2024',
                'advertisement_no' => 'ADV/2024/002',
                'publish_date_from' => '2024-01-15',
                'publish_date_to' => '2024-11-30'
            ],
            [
                'id' => 3,
                'exercise_id' => 3,
                'org_id' => 3,
                'position_group_id' => 3,
                'position_reference' => 'GOV/TREAS/2024/003',
                'designation' => 'Financial Analyst',
                'classification' => 'Professional Officer',
                'award' => 'PNG-6',
                'location' => 'Port Moresby, National Capital District',
                'annual_salary' => 'K65,000 - K75,000',
                'qualifications' => 'Bachelor\'s degree in Finance, Accounting, or Economics',
                'knowledge' => 'Financial analysis, budget preparation, government accounting',
                'skills_competencies' => 'CPA qualification, Excel, Financial modeling',
                'job_experiences' => '3+ years in financial analysis',
                'jd_filepath' => '/uploads/jd/financial_analyst_2024.pdf',
                'jd_texts_extracted' => 'Financial Analyst position for budget and financial planning...',
                'remarks' => 'Essential for budget planning',
                'status' => 1,
                'status_at' => '2024-02-01 09:45:00',
                'status_by' => 3,
                'status_remarks' => 'Approved for immediate posting',
                'created_by' => 3,
                'updated_by' => 3,
                'created_at' => '2024-01-10 08:30:00',
                'updated_at' => '2024-02-01 09:45:00',
                'org_name' => 'Department of Treasury',
                'orgcode' => 'DOT',
                'group_name' => 'Financial Analysis Unit',
                'exercise_name' => 'Treasury Financial Recruitment 2024',
                'advertisement_no' => 'ADV/2024/003',
                'publish_date_from' => '2024-02-01',
                'publish_date_to' => '2024-12-15'
            ],
            [
                'id' => 4,
                'exercise_id' => 4,
                'org_id' => 4,
                'position_group_id' => 4,
                'position_reference' => 'GOV/DPM/2024/004',
                'designation' => 'Human Resources Officer',
                'classification' => 'Administrative Officer',
                'award' => 'PNG-5',
                'location' => 'Mount Hagen, Western Highlands Province',
                'annual_salary' => 'K55,000 - K65,000',
                'qualifications' => 'Bachelor\'s degree in Human Resources or related field',
                'knowledge' => 'HR policies, employment law, recruitment processes',
                'skills_competencies' => 'HRIS systems, Communication, Conflict resolution',
                'job_experiences' => '2+ years in human resources',
                'jd_filepath' => '/uploads/jd/hr_officer_2024.pdf',
                'jd_texts_extracted' => 'Human Resources Officer position for regional operations...',
                'remarks' => 'Regional HR support required',
                'status' => 1,
                'status_at' => '2024-01-25 13:20:00',
                'status_by' => 4,
                'status_remarks' => 'Approved for regional posting',
                'created_by' => 4,
                'updated_by' => 4,
                'created_at' => '2024-01-08 10:15:00',
                'updated_at' => '2024-01-25 13:20:00',
                'org_name' => 'Department of Personnel Management',
                'orgcode' => 'DPM',
                'group_name' => 'HR Operations',
                'exercise_name' => 'Personnel Management Recruitment 2024',
                'advertisement_no' => 'ADV/2024/004',
                'publish_date_from' => '2024-01-20',
                'publish_date_to' => '2024-11-25'
            ],
            [
                'id' => 5,
                'exercise_id' => 5,
                'org_id' => 5,
                'position_group_id' => 5,
                'position_reference' => 'GOV/DEC/2024/005',
                'designation' => 'Environmental Scientist',
                'classification' => 'Scientific Officer',
                'award' => 'PNG-7',
                'location' => 'Madang, Madang Province',
                'annual_salary' => 'K70,000 - K80,000',
                'qualifications' => 'Bachelor\'s degree in Environmental Science or related field',
                'knowledge' => 'Environmental assessment, conservation, research methodologies',
                'skills_competencies' => 'GIS, Data analysis, Report writing, Field research',
                'job_experiences' => '3+ years in environmental research',
                'jd_filepath' => '/uploads/jd/environmental_scientist_2024.pdf',
                'jd_texts_extracted' => 'Environmental Scientist position for conservation research...',
                'remarks' => 'Critical for environmental monitoring',
                'status' => 1,
                'status_at' => '2024-02-10 11:30:00',
                'status_by' => 5,
                'status_remarks' => 'Approved for coastal region',
                'created_by' => 5,
                'updated_by' => 5,
                'created_at' => '2024-01-12 14:00:00',
                'updated_at' => '2024-02-10 11:30:00',
                'org_name' => 'Department of Environment and Conservation',
                'orgcode' => 'DEC',
                'group_name' => 'Environmental Research Division',
                'exercise_name' => 'Environmental Conservation Recruitment 2024',
                'advertisement_no' => 'ADV/2024/005',
                'publish_date_from' => '2024-02-15',
                'publish_date_to' => '2024-12-20'
            ]
        ];

        // Dummy organizations data based on DakoiiOrgModel (orgModel) fields
        $organizations = [
            [
                'id' => 1,
                'org_code' => 'DICT',
                'org_name' => 'Department of Information and Communication Technology',
                'name' => 'Department of Information and Communication Technology', // For compatibility
                'description' => 'Leading PNG\'s digital transformation and ICT development',
                'location_lock_province' => 'National Capital District',
                'location_lock_country' => 'Papua New Guinea',
                'logo_path' => '/uploads/logos/dict_logo.png',
                'is_locationlocked' => 0,
                'postal_address' => 'P.O. Box 1234, Port Moresby, NCD',
                'phone_numbers' => '+************, +************',
                'email_addresses' => '<EMAIL>, <EMAIL>',
                'is_active' => 1,
                'license_status' => 'active',
                'created_by' => 1,
                'updated_by' => 1,
                'created_at' => '2024-01-01 00:00:00',
                'updated_at' => '2024-01-01 00:00:00'
            ],
            [
                'id' => 2,
                'org_code' => 'DWI',
                'org_name' => 'Department of Works and Implementation',
                'name' => 'Department of Works and Implementation',
                'description' => 'Infrastructure development and public works management',
                'location_lock_province' => 'Morobe',
                'location_lock_country' => 'Papua New Guinea',
                'logo_path' => '/uploads/logos/dwi_logo.png',
                'is_locationlocked' => 1,
                'postal_address' => 'P.O. Box 5678, Lae, Morobe',
                'phone_numbers' => '+************, +************',
                'email_addresses' => '<EMAIL>, <EMAIL>',
                'is_active' => 1,
                'license_status' => 'active',
                'created_by' => 1,
                'updated_by' => 1,
                'created_at' => '2024-01-01 00:00:00',
                'updated_at' => '2024-01-01 00:00:00'
            ],
            [
                'id' => 3,
                'org_code' => 'DOT',
                'org_name' => 'Department of Treasury',
                'name' => 'Department of Treasury',
                'description' => 'Government financial management and budget oversight',
                'location_lock_province' => 'National Capital District',
                'location_lock_country' => 'Papua New Guinea',
                'logo_path' => '/uploads/logos/dot_logo.png',
                'is_locationlocked' => 0,
                'postal_address' => 'P.O. Box 9876, Port Moresby, NCD',
                'phone_numbers' => '+************, +************',
                'email_addresses' => '<EMAIL>, <EMAIL>',
                'is_active' => 1,
                'license_status' => 'active',
                'created_by' => 1,
                'updated_by' => 1,
                'created_at' => '2024-01-01 00:00:00',
                'updated_at' => '2024-01-01 00:00:00'
            ],
            [
                'id' => 4,
                'org_code' => 'DPM',
                'org_name' => 'Department of Personnel Management',
                'name' => 'Department of Personnel Management',
                'description' => 'Public service human resource management',
                'location_lock_province' => 'Western Highlands',
                'location_lock_country' => 'Papua New Guinea',
                'logo_path' => '/uploads/logos/dpm_logo.png',
                'is_locationlocked' => 1,
                'postal_address' => 'P.O. Box 4321, Mount Hagen, WHP',
                'phone_numbers' => '+************, +************',
                'email_addresses' => '<EMAIL>, <EMAIL>',
                'is_active' => 1,
                'license_status' => 'active',
                'created_by' => 1,
                'updated_by' => 1,
                'created_at' => '2024-01-01 00:00:00',
                'updated_at' => '2024-01-01 00:00:00'
            ],
            [
                'id' => 5,
                'org_code' => 'DEC',
                'org_name' => 'Department of Environment and Conservation',
                'name' => 'Department of Environment and Conservation',
                'description' => 'Environmental protection and conservation management',
                'location_lock_province' => 'Madang',
                'location_lock_country' => 'Papua New Guinea',
                'logo_path' => '/uploads/logos/dec_logo.png',
                'is_locationlocked' => 1,
                'postal_address' => 'P.O. Box 7890, Madang, Madang',
                'phone_numbers' => '+************, +************',
                'email_addresses' => '<EMAIL>, <EMAIL>',
                'is_active' => 1,
                'license_status' => 'active',
                'created_by' => 1,
                'updated_by' => 1,
                'created_at' => '2024-01-01 00:00:00',
                'updated_at' => '2024-01-01 00:00:00'
            ]
        ];

        // Extract unique classifications and locations for filtering
        $classifications = array_unique(array_filter(array_column($positions, 'classification')));
        $locations = array_unique(array_filter(array_column($positions, 'location')));

        // Return the view with dummy data that matches actual model structure
        return view('home/home_jobs', [
            'title' => 'Available Positions',
            'positions' => $positions,
            'organizations' => $organizations,
            'classifications' => $classifications,
            'locations' => $locations
        ]);
    }

    public function view($id = null)
    {
        if ($id === null) {
            return redirect()->to('/jobs');
        }

        // Dummy position details data based on actual model fields
        $dummyPositions = [
            1 => [
                // From positions table (PositionsModel) - all fields
                'id' => 1,
                'exercise_id' => 1,
                'org_id' => 1,
                'position_group_id' => 1,
                'position_reference' => 'GOV/IT/2024/001',
                'designation' => 'Senior Software Developer',
                'classification' => 'Professional Officer',
                'award' => 'PNG-7',
                'location' => 'Port Moresby, National Capital District',
                'annual_salary' => 'K85,000 - K95,000',
                'qualifications' => 'Bachelor\'s degree in Computer Science, Information Technology, or related field. Master\'s degree preferred.',
                'knowledge' => 'Extensive knowledge in software development methodologies, database management systems, web technologies, system architecture, and government IT policies.',
                'skills_competencies' => 'Advanced proficiency in PHP, JavaScript, Python, MySQL, PostgreSQL, CodeIgniter, Laravel, Git, Docker, AWS, project management, and team leadership.',
                'job_experiences' => 'Minimum 5 years of progressive experience in software development with at least 2 years in a senior role. Experience in government or public sector preferred.',
                'jd_filepath' => '/uploads/jd/senior_developer_2024.pdf',
                'jd_texts_extracted' => 'The Senior Software Developer will lead the development of critical government applications, mentor junior developers, and ensure adherence to security and quality standards...',
                'remarks' => 'Priority position for digital transformation initiative. Requires security clearance.',
                'status' => 1,
                'status_at' => '2024-01-15 10:30:00',
                'status_by' => 1,
                'status_remarks' => 'Approved for publication by IT Director',
                'created_by' => 1,
                'updated_by' => 1,
                'created_at' => '2024-01-01 09:00:00',
                'updated_at' => '2024-01-15 10:30:00',
                'deleted_at' => null,
                'deleted_by' => null,
                // From dakoii_org table (joined)
                'org_name' => 'Department of Information and Communication Technology',
                'orgcode' => 'DICT',
                // From positions_groups table (joined)
                'group_name' => 'IT Development Group',
                // From exercises table (joined)
                'exercise_name' => 'Government ICT Recruitment Exercise 2024',
                'advertisement_no' => 'ADV/2024/001',
                'exercise_status' => 'publish',
                'publish_date_from' => '2024-01-01',
                'publish_date_to' => '2024-12-31'
            ],
            2 => [
                'id' => 2,
                'exercise_id' => 2,
                'org_id' => 2,
                'position_group_id' => 2,
                'position_reference' => 'GOV/WORKS/2024/002',
                'designation' => 'Project Manager - Infrastructure',
                'classification' => 'Management Officer',
                'award' => 'PNG-8',
                'location' => 'Lae, Morobe Province',
                'annual_salary' => 'K75,000 - K85,000',
                'qualifications' => 'Bachelor\'s degree in Engineering (Civil, Mechanical, or related), Project Management, or equivalent. PMP certification highly desirable.',
                'knowledge' => 'Comprehensive understanding of infrastructure development, project management methodologies, contract management, stakeholder engagement, and PNG construction standards.',
                'skills_competencies' => 'PMP or equivalent certification, MS Project, Primavera, AutoCAD, risk management, budget management, team leadership, and excellent communication skills.',
                'job_experiences' => 'Minimum 7 years of project management experience with at least 3 years managing large-scale infrastructure projects. Government sector experience preferred.',
                'jd_filepath' => '/uploads/jd/project_manager_2024.pdf',
                'jd_texts_extracted' => 'The Project Manager will oversee major infrastructure development projects, coordinate with multiple stakeholders, and ensure projects are delivered on time and within budget...',
                'remarks' => 'Critical position for national infrastructure development program',
                'status' => 1,
                'status_at' => '2024-01-20 14:15:00',
                'status_by' => 2,
                'status_remarks' => 'Approved for urgent recruitment due to project demands',
                'created_by' => 2,
                'updated_by' => 2,
                'created_at' => '2024-01-05 11:00:00',
                'updated_at' => '2024-01-20 14:15:00',
                'deleted_at' => null,
                'deleted_by' => null,
                'org_name' => 'Department of Works and Implementation',
                'orgcode' => 'DWI',
                'group_name' => 'Infrastructure Management',
                'exercise_name' => 'Infrastructure Development Recruitment 2024',
                'advertisement_no' => 'ADV/2024/002',
                'exercise_status' => 'publish',
                'publish_date_from' => '2024-01-15',
                'publish_date_to' => '2024-11-30'
            ],
            3 => [
                'id' => 3,
                'exercise_id' => 3,
                'org_id' => 3,
                'position_group_id' => 3,
                'position_reference' => 'GOV/TREAS/2024/003',
                'designation' => 'Financial Analyst',
                'classification' => 'Professional Officer',
                'award' => 'PNG-6',
                'location' => 'Port Moresby, National Capital District',
                'annual_salary' => 'K65,000 - K75,000',
                'qualifications' => 'Bachelor\'s degree in Finance, Accounting, Economics, or related field. CPA or equivalent professional qualification preferred.',
                'knowledge' => 'Strong foundation in financial analysis, government accounting principles, budget preparation, economic analysis, and financial reporting standards.',
                'skills_competencies' => 'Advanced Excel, financial modeling, data analysis, CPA qualification, MYOB/SAP experience, analytical thinking, and attention to detail.',
                'job_experiences' => 'Minimum 3 years of experience in financial analysis, preferably in government or public sector. Experience with budget preparation and financial reporting required.',
                'jd_filepath' => '/uploads/jd/financial_analyst_2024.pdf',
                'jd_texts_extracted' => 'The Financial Analyst will support budget planning, conduct financial analysis, prepare reports for senior management, and ensure compliance with financial regulations...',
                'remarks' => 'Essential position for annual budget planning cycle',
                'status' => 1,
                'status_at' => '2024-02-01 09:45:00',
                'status_by' => 3,
                'status_remarks' => 'Approved for immediate posting to support budget cycle',
                'created_by' => 3,
                'updated_by' => 3,
                'created_at' => '2024-01-10 08:30:00',
                'updated_at' => '2024-02-01 09:45:00',
                'deleted_at' => null,
                'deleted_by' => null,
                'org_name' => 'Department of Treasury',
                'orgcode' => 'DOT',
                'group_name' => 'Financial Analysis Unit',
                'exercise_name' => 'Treasury Financial Recruitment 2024',
                'advertisement_no' => 'ADV/2024/003',
                'exercise_status' => 'publish',
                'publish_date_from' => '2024-02-01',
                'publish_date_to' => '2024-12-15'
            ],
            4 => [
                'id' => 4,
                'exercise_id' => 4,
                'org_id' => 4,
                'position_group_id' => 4,
                'position_reference' => 'GOV/DPM/2024/004',
                'designation' => 'Human Resources Officer',
                'classification' => 'Administrative Officer',
                'award' => 'PNG-5',
                'location' => 'Mount Hagen, Western Highlands Province',
                'annual_salary' => 'K55,000 - K65,000',
                'qualifications' => 'Bachelor\'s degree in Human Resources, Business Administration, Psychology, or related field. HR certification preferred.',
                'knowledge' => 'Comprehensive understanding of HR policies, employment law, recruitment processes, performance management, and industrial relations.',
                'skills_competencies' => 'HRIS systems, recruitment and selection, employee relations, training coordination, policy development, communication, and conflict resolution.',
                'job_experiences' => 'Minimum 2 years of experience in human resources, preferably in government or large organization. Experience in regional operations advantageous.',
                'jd_filepath' => '/uploads/jd/hr_officer_2024.pdf',
                'jd_texts_extracted' => 'The Human Resources Officer will provide comprehensive HR support to regional operations, manage recruitment processes, and ensure compliance with employment regulations...',
                'remarks' => 'Regional position to support decentralized HR operations',
                'status' => 1,
                'status_at' => '2024-01-25 13:20:00',
                'status_by' => 4,
                'status_remarks' => 'Approved for regional posting to strengthen HR presence',
                'created_by' => 4,
                'updated_by' => 4,
                'created_at' => '2024-01-08 10:15:00',
                'updated_at' => '2024-01-25 13:20:00',
                'deleted_at' => null,
                'deleted_by' => null,
                'org_name' => 'Department of Personnel Management',
                'orgcode' => 'DPM',
                'group_name' => 'HR Operations',
                'exercise_name' => 'Personnel Management Recruitment 2024',
                'advertisement_no' => 'ADV/2024/004',
                'exercise_status' => 'publish',
                'publish_date_from' => '2024-01-20',
                'publish_date_to' => '2024-11-25'
            ],
            5 => [
                'id' => 5,
                'exercise_id' => 5,
                'org_id' => 5,
                'position_group_id' => 5,
                'position_reference' => 'GOV/DEC/2024/005',
                'designation' => 'Environmental Scientist',
                'classification' => 'Scientific Officer',
                'award' => 'PNG-7',
                'location' => 'Madang, Madang Province',
                'annual_salary' => 'K70,000 - K80,000',
                'qualifications' => 'Bachelor\'s degree in Environmental Science, Biology, Chemistry, or related field. Master\'s degree in environmental studies preferred.',
                'knowledge' => 'Extensive knowledge in environmental assessment methodologies, conservation principles, research techniques, environmental legislation, and impact assessment.',
                'skills_competencies' => 'GIS software, statistical analysis, field research techniques, report writing, data collection and analysis, environmental monitoring, and stakeholder engagement.',
                'job_experiences' => 'Minimum 3 years of experience in environmental research or conservation work. Field research experience in tropical environments preferred.',
                'jd_filepath' => '/uploads/jd/environmental_scientist_2024.pdf',
                'jd_texts_extracted' => 'The Environmental Scientist will conduct research on coastal and marine ecosystems, monitor environmental impacts, and develop conservation strategies...',
                'remarks' => 'Critical position for coastal environmental monitoring program',
                'status' => 1,
                'status_at' => '2024-02-10 11:30:00',
                'status_by' => 5,
                'status_remarks' => 'Approved for coastal region assignment',
                'created_by' => 5,
                'updated_by' => 5,
                'created_at' => '2024-01-12 14:00:00',
                'updated_at' => '2024-02-10 11:30:00',
                'deleted_at' => null,
                'deleted_by' => null,
                'org_name' => 'Department of Environment and Conservation',
                'orgcode' => 'DEC',
                'group_name' => 'Environmental Research Division',
                'exercise_name' => 'Environmental Conservation Recruitment 2024',
                'advertisement_no' => 'ADV/2024/005',
                'exercise_status' => 'publish',
                'publish_date_from' => '2024-02-15',
                'publish_date_to' => '2024-12-20'
            ]
        ];

        // Get the position by ID
        $position = $dummyPositions[$id] ?? null;

        if ($position === null) {
            return redirect()->to('/jobs');
        }

        return view('home/home_job_details', [
            'position' => $position
        ]);
    }



    public function logout()
    {
        // Destroy the user's session
        $session = session();
        $session->destroy();

        // Redirect to the login page
        return redirect()->to(base_url());
    }

    public function loginForm()
    {
        return view('home/home_login', ['title' => 'Login', 'menu' => 'login']);
    }

    public function processLogin()
    {
        if (!$this->request->getPost('username') || !$this->request->getPost('password')) {
            return redirect()->back()->with('error', 'Username and password are required');
        }

        $username = $this->request->getPost('username');
        $password = $this->request->getPost('password');

        $user = $this->usersModel->where('username', $username)->first();

        if ($user && password_verify($password, $user['password'])) {
            if ($user['status'] != 1) {
                return redirect()->back()->with('error', 'Your account is inactive. Please contact administrator.');
            }

            $this->session->set([
                'logged_in' => true,
                'user_id' => $user['id'],
                'name' => $user['name'],
                'role' => $user['role'],
                'org_id' => $user['org_id']
            ]);

            return redirect()->to('dashboard')->with('success', "Welcome back, {$user['name']}! You've successfully logged in.");
        }

        return redirect()->back()->with('error', 'Invalid username or password. Please try again.');
    }

    public function registerForm()
    {
        return view('home/home_register', ['title' => 'Register', 'menu' => 'register']);
    }

    public function processRegister()
    {
        // Log the incoming request data
        log_message('debug', 'Registration attempt with data: ' . json_encode($this->request->getPost()));

        // Validate form data
        $rules = [
            'firstname' => 'required|min_length[3]|max_length[50]',
            'lastname' => 'required|min_length[3]|max_length[50]',
            'email' => 'required|valid_email|is_unique[applicants.email]',
            'password' => 'required|min_length[4]',
            'confirm_password' => 'required|matches[password]'
        ];

        if (!$this->validate($rules)) {
            $errors = $this->validator->getErrors();
            log_message('debug', 'Validation failed with errors: ' . json_encode($errors));
            return redirect()->back()->withInput()->with('errors', $errors);
        }

        // Generate unique ID and activation token
        $unique_id = 'APP' . time() . rand(1000, 9999);
        $activation_token = bin2hex(random_bytes(32));

        // Prepare data for insertion
        $data = [
            'unique_id' => $unique_id,
            'fname' => $this->request->getPost('firstname'),
            'lname' => $this->request->getPost('lastname'),
            'email' => $this->request->getPost('email'),
            'password' => $this->request->getPost('password'), // Model will hash this
            'activation_token' => $activation_token,
            'status' => 'pending'
        ];

        try {
            // Save applicant data
            $inserted = $this->applicantsModel->insert($data);
            log_message('debug', 'Applicant data insertion result: ' . ($inserted ? 'success' : 'failed'));

            if (!$inserted) {
                log_message('error', 'Failed to insert applicant data: ' . json_encode($this->applicantsModel->errors()));
                return redirect()->back()->withInput()->with('error', 'Failed to create account. Please try again.');
            }

            // Send activation email
            $email = \Config\Services::email();

            $emailConfig = [
                'protocol' => 'smtp',
                'SMTPHost' => 'mail.dakoiims.com',
                'SMTPUser' => '<EMAIL>',
                'SMTPPass' => 'dakoiianzii',
                'SMTPPort' => 465,
                'SMTPCrypto' => 'ssl',
                'mailType' => 'html'
            ];

            $email->initialize($emailConfig);
            $email->setFrom('<EMAIL>', 'GRASS System');
            $email->setTo($data['email']);
            $email->setSubject('Activate Your GRASS Account');

            $activation_link = base_url("applicant/activate/{$activation_token}");
            $email_body = view('emails/emails_activation_email', [
                'firstname' => $data['fname'],
                'activation_link' => $activation_link
            ]);

            $email->setMessage($email_body);

            $emailSent = $email->send();
            log_message('debug', 'Email sending result: ' . ($emailSent ? 'success' : 'failed'));
            if (!$emailSent) {
                log_message('error', 'Email error: ' . $email->printDebugger(['headers']));
            }

            if ($emailSent) {
                session()->setFlashdata('swal_icon', 'success');
                session()->setFlashdata('swal_title', 'Registration Successful!');
                session()->setFlashdata('swal_text', 'Please check your email to activate your account.');
                return redirect()->to('/');
            } else {
                session()->setFlashdata('swal_icon', 'warning');
                session()->setFlashdata('swal_title', 'Account Created');
                session()->setFlashdata('swal_text', 'Account created but activation email could not be sent. Please contact support.');
                return redirect()->to('/');
            }
        } catch (\Exception $e) {
            log_message('error', 'Registration error: ' . $e->getMessage());
            session()->setFlashdata('swal_icon', 'error');
            session()->setFlashdata('swal_title', 'Registration Failed');
            session()->setFlashdata('swal_text', 'An error occurred during registration. Please try again.');
            return redirect()->back()->withInput();
        }
    }

    public function activate($token)
    {
        $applicant = $this->applicantsModel->where('activation_token', $token)->first();

        if (!$applicant) {
            session()->setFlashdata('swal_icon', 'error');
            session()->setFlashdata('swal_title', 'Invalid Token');
            session()->setFlashdata('swal_text', 'The activation token is invalid or has expired.');
            return redirect()->to('/');
        }

        if ($applicant['status'] === 'active') {
            session()->setFlashdata('swal_icon', 'info');
            session()->setFlashdata('swal_title', 'Already Activated');
            session()->setFlashdata('swal_text', 'Your account is already activated. Please login.');
            return redirect()->to('/');
        }

        // Activate the account
        $this->applicantsModel->update($applicant['applicant_id'], [
            'status' => 'active',
            'activation_token' => null
        ]);

        session()->setFlashdata('swal_icon', 'success');
        session()->setFlashdata('swal_title', 'Account Activated');
        session()->setFlashdata('swal_text', 'Your account has been activated successfully. You can now login.');
        return redirect()->to('/');
    }

    public function applicantLoginForm()
    {
        return view('home/home_applicant_login', ['title' => 'Applicant Login', 'menu' => 'applicant_login']);
    }

    public function processApplicantLogin()
    {
        $email = $this->request->getPost('email');
        $password = $this->request->getPost('password');

        $applicant = $this->applicantsModel->where('email', $email)->first();

        if ($applicant && password_verify($password, $applicant['password'])) {
            if ($applicant['status'] !== 'active') {
                session()->setFlashdata('swal_icon', 'warning');
                session()->setFlashdata('swal_title', 'Account Not Activated');
                session()->setFlashdata('swal_text', 'Please activate your account first. Check your email for the activation link.');
                return redirect()->back();
            }

            $this->session->set([
                'logged_in' => true,
                'applicant_id' => $applicant['applicant_id'],
                'applicant_name' => $applicant['fname'] . ' ' . $applicant['lname'],
                'applicant_email' => $applicant['email']
            ]);

            session()->setFlashdata('swal_icon', 'success');
            session()->setFlashdata('swal_title', 'Welcome Back!');
            session()->setFlashdata('swal_text', 'Welcome back, ' . $applicant['fname'] . '!');
            return redirect()->to('applicant/dashboard');
        }

        session()->setFlashdata('swal_icon', 'error');
        session()->setFlashdata('swal_title', 'Login Failed');
        session()->setFlashdata('swal_text', 'Invalid email or password.');
        return redirect()->back();
    }
}
