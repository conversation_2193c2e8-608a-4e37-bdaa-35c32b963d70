<?php

namespace App\Controllers;

use App\Models\AdxItemsModel;
use App\Models\AdxItemsScoresModel;
use CodeIgniter\RESTful\ResourceController;

class DakoiiRatingsController extends ResourceController
{
    protected $itemsModel;
    protected $scoresModel;

    public function __construct()
    {
        $this->itemsModel = new AdxItemsModel();
        $this->scoresModel = new AdxItemsScoresModel();
    }

    /**
     * List all rating items
     *
     * @return mixed
     */
    public function index()
    {
        $data = [
            'title' => 'Rating Items',
            'menu' => 'rating_items',
            'items' => $this->itemsModel->getAllItems()
        ];

        return view('dakoii_ratings/dakoii_ratings_list', $data);
    }

    /**
     * Show form to create a new rating item
     *
     * @return mixed
     */
    public function new()
    {
        $data = [
            'title' => 'Add Rating Item',
            'menu' => 'rating_items'
        ];

        return view('dakoii_ratings/dakoii_ratings_create', $data);
    }

    /**
     * Create a new rating item
     *
     * @return mixed
     */
    public function create()
    {
        // Validate input
        $validation = $this->validate([
            'item_label' => 'required|max_length[255]'
        ]);

        if (!$validation) {
            return redirect()->back()->withInput()->with('error', $this->validator->getErrors());
        }

        // Prepare data for insert
        $data = [
            'item_label' => $this->request->getPost('item_label'),
            'created_by' => session()->get('user_id') ?? 1
        ];

        // Insert the item
        if ($this->itemsModel->insert($data)) {
            return redirect()->to('/dakoii/rating_items/list')->with('success', 'Rating item created successfully');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create rating item');
        }
    }

    /**
     * Show form to edit a rating item
     *
     * @param int $id
     * @return mixed
     */
    public function edit($id = null)
    {
        $item = $this->itemsModel->getItemById($id);

        if (empty($item)) {
            return redirect()->to('/dakoii/rating_items/list')->with('error', 'Rating item not found');
        }

        $data = [
            'title' => 'Edit Rating Item',
            'menu' => 'rating_items',
            'item' => $item,
            'scores' => $this->scoresModel->getScoresByItemId($id)
        ];

        return view('dakoii_ratings/dakoii_ratings_edit', $data);
    }

    /**
     * Update a rating item
     *
     * @param int $id
     * @return mixed
     */
    public function update($id = null)
    {
        // Validate input
        $validation = $this->validate([
            'item_label' => 'required|max_length[255]'
        ]);

        if (!$validation) {
            return redirect()->back()->withInput()->with('error', $this->validator->getErrors());
        }

        // Prepare data for update
        $data = [
            'item_label' => $this->request->getPost('item_label'),
            'updated_by' => session()->get('user_id') ?? 1
        ];

        // Update the item
        if ($this->itemsModel->update($id, $data)) {
            return redirect()->to('/dakoii/rating_items/list')->with('success', 'Rating item updated successfully');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update rating item');
        }
    }

    /**
     * Delete a rating item
     *
     * @param int $id
     * @return mixed
     */
    public function delete($id = null)
    {
        // Check if item exists
        $item = $this->itemsModel->getItemById($id);
        
        if (empty($item)) {
            return redirect()->to('/dakoii/rating_items/list')->with('error', 'Rating item not found');
        }

        // Delete the item
        if ($this->itemsModel->delete($id)) {
            // When an item is deleted, its scores will be deleted automatically
            // due to foreign key constraints (if configured in the database)
            return redirect()->to('/dakoii/rating_items/list')->with('success', 'Rating item deleted successfully');
        } else {
            return redirect()->to('/dakoii/rating_items/list')->with('error', 'Failed to delete rating item');
        }
    }

    /**
     * Show the add score form
     *
     * @param int $itemId
     * @return mixed
     */
    public function newScore($itemId = null)
    {
        $item = $this->itemsModel->getItemById($itemId);
        
        if (empty($item)) {
            return redirect()->to('/dakoii/rating_items/list')->with('error', 'Rating item not found');
        }

        $data = [
            'title' => 'Add Rating Score',
            'menu' => 'rating_items',
            'item' => $item
        ];

        return view('dakoii_ratings/dakoii_ratings_scores_create', $data);
    }

    /**
     * Create a new score for an item
     *
     * @return mixed
     */
    public function createScore()
    {
        // Validate input
        $validation = $this->validate([
            'item_id' => 'required|numeric',
            'score' => 'required|numeric',
            'score_description' => 'permit_empty|max_length[255]'
        ]);

        if (!$validation) {
            return redirect()->back()->withInput()->with('error', $this->validator->getErrors());
        }

        // Prepare data for insert
        $data = [
            'item_id' => $this->request->getPost('item_id'),
            'score' => $this->request->getPost('score'),
            'score_description' => $this->request->getPost('score_description'),
            'created_by' => session()->get('user_id') ?? 1
        ];

        // Insert the score
        if ($this->scoresModel->insert($data)) {
            return redirect()->to('/dakoii/rating_items/edit/' . $data['item_id'])->with('success', 'Rating score created successfully');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create rating score');
        }
    }

    /**
     * Show form to edit a score
     *
     * @param int $id
     * @return mixed
     */
    public function editScore($id = null)
    {
        $score = $this->scoresModel->getScoreById($id);
        
        if (empty($score)) {
            return redirect()->to('/dakoii/rating_items/list')->with('error', 'Rating score not found');
        }

        $item = $this->itemsModel->getItemById($score['item_id']);

        $data = [
            'title' => 'Edit Rating Score',
            'menu' => 'rating_items',
            'score' => $score,
            'item' => $item
        ];

        return view('dakoii_ratings/dakoii_ratings_scores_edit', $data);
    }

    /**
     * Update a score
     *
     * @param int $id
     * @return mixed
     */
    public function updateScore($id = null)
    {
        // Validate input
        $validation = $this->validate([
            'score' => 'required|numeric',
            'score_description' => 'permit_empty|max_length[255]'
        ]);

        if (!$validation) {
            return redirect()->back()->withInput()->with('error', $this->validator->getErrors());
        }

        $score = $this->scoresModel->getScoreById($id);
        
        if (empty($score)) {
            return redirect()->to('/dakoii/rating_items/list')->with('error', 'Rating score not found');
        }

        // Prepare data for update
        $data = [
            'score' => $this->request->getPost('score'),
            'score_description' => $this->request->getPost('score_description'),
            'updated_by' => session()->get('user_id') ?? 1
        ];

        // Update the score
        if ($this->scoresModel->update($id, $data)) {
            return redirect()->to('/dakoii/rating_items/edit/' . $score['item_id'])->with('success', 'Rating score updated successfully');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update rating score');
        }
    }

    /**
     * Delete a score
     *
     * @param int $id
     * @return mixed
     */
    public function deleteScore($id = null)
    {
        $score = $this->scoresModel->getScoreById($id);
        
        if (empty($score)) {
            return redirect()->to('/dakoii/rating_items/list')->with('error', 'Rating score not found');
        }

        $itemId = $score['item_id'];

        // Delete the score
        if ($this->scoresModel->delete($id)) {
            return redirect()->to('/dakoii/rating_items/edit/' . $itemId)->with('success', 'Rating score deleted successfully');
        } else {
            return redirect()->to('/dakoii/rating_items/edit/' . $itemId)->with('error', 'Failed to delete rating score');
        }
    }
} 