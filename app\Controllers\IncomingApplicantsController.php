<?php

namespace App\Controllers;

use App\Models\APPX_ApplicationInformationModel;
use App\Models\applicantsModel;
use CodeIgniter\API\ResponseTrait;

class IncomingApplicantsController extends BaseController
{
    use ResponseTrait;
    
    protected $applicationModel;
    protected $applicantsModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url']);
        $this->session = \Config\Services::session();
        $this->applicationModel = new APPX_ApplicationInformationModel();
        $this->applicantsModel = new applicantsModel();
    }

    /**
     * Display the list of unacknowledged applications
     */
    public function index()
    {
        // Get all applications where recieved_acknowledged is empty (using correct column name)
        $applications = $this->applicationModel
            ->where('recieved_acknowledged IS NULL OR recieved_acknowledged = ""')
            ->findAll();

        $data = [
            'title' => 'Incoming Applications',
            'menu' => 'applications',
            'applications' => $applications
        ];

        return view('incoming_applicants/incoming_applicants_list', $data);
    }

    /**
     * Mark an application as received/acknowledged
     */
    public function acknowledge($id)
    {
        $data = [
            'recieved_acknowledged' => date('Y-m-d H:i:s'),
            'recieved_by' => $this->session->get('user_id'),
            'recieved_at' => date('Y-m-d H:i:s'),
            'updated_by' => $this->session->get('user_id'),
            'status' => 'active'
        ];

        try {
            if ($this->applicationModel->update($id, $data)) {
                // Get the application details for email notification
                $application = $this->applicationModel->find($id);
                
                // Send email notification
                $emailSent = false;
                if ($application) {
                    $emailSent = $this->sendAcknowledgmentEmail($application);
                    log_message('debug', 'Acknowledgment email result: ' . ($emailSent ? 'success' : 'failed'));
                } else {
                    log_message('notice', 'Could not send acknowledgment email - application not found: ' . $id);
                }
                
                return $this->response->setJSON([
                    'success' => true, 
                    'message' => 'Application successfully acknowledged',
                    'email_sent' => $emailSent,
                    'csrf_hash' => csrf_hash()
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false, 
                    'message' => 'Failed to acknowledge application',
                    'csrf_hash' => csrf_hash()
                ]);
            }
        } catch (\Exception $e) {
            log_message('error', 'Error acknowledging application: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false, 
                'message' => 'Error: ' . $e->getMessage(),
                'csrf_hash' => csrf_hash()
            ]);
        }
    }

    /**
     * Send acknowledgment email to applicant
     */
    private function sendAcknowledgmentEmail($application)
    {
        try {
            // Get the applicant's email address from the applicants table
            $applicantId = $application['applicant_id'];
            $applicant = $this->applicantsModel->find($applicantId);
            
            if (!$applicant || empty($applicant['email'])) {
                log_message('notice', 'Could not send acknowledgment email - no email found for applicant ID: ' . $applicantId);
                return false;
            }
            
            // Initialize email service
            $email = \Config\Services::email();
            
            $emailConfig = [
                'protocol' => 'smtp',
                'SMTPHost' => 'mail.dakoiims.com',
                'SMTPUser' => '<EMAIL>',
                'SMTPPass' => 'dakoiianzii',
                'SMTPPort' => 465,
                'SMTPCrypto' => 'ssl',
                'mailType' => 'html'
            ];
            
            $email->initialize($emailConfig);
            $email->setFrom('<EMAIL>', 'GRASS System');
            $email->setTo($applicant['email']);
            $email->setSubject('Your Application Has Been Received - ' . $application['application_number']);
            
            // Create email content
            $emailBody = '
            <html>
            <head>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background-color: #f8f9fa; padding: 20px; text-align: center; }
                    .content { padding: 20px; }
                    .footer { font-size: 12px; text-align: center; margin-top: 20px; color: #777; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h2>Application Acknowledgment</h2>
                    </div>
                    <div class="content">
                        <p>Dear ' . $application['fname'] . ' ' . $application['lname'] . ',</p>
                        <p>We are pleased to inform you that your application (Reference: <strong>' . $application['application_number'] . '</strong>) has been received and acknowledged by our team.</p>
                        <p>Your application status has been updated to <strong>ACTIVE</strong>. It is now being processed by our recruitment team.</p>
                        <p>We will contact you regarding the next steps in the recruitment process. Please keep this email for your records.</p>
                        <p>Thank you for your interest in our organization.</p>
                        <p>Best regards,<br>Recruitment Team</p>
                    </div>
                    <div class="footer">
                        <p>This is an automated message. Please do not reply to this email.</p>
                    </div>
                </div>
            </body>
            </html>';
            
            $email->setMessage($emailBody);
            
            // Send the email
            $result = $email->send();
            
            // Log email debug information if sending failed
            if (!$result) {
                log_message('error', 'Acknowledgment email error: ' . $email->printDebugger(['headers']));
            } else {
                log_message('info', 'Acknowledgment email sent successfully to: ' . $applicant['email']);
            }
            
            return $result;
        } catch (\Exception $e) {
            log_message('error', 'Exception while sending acknowledgment email: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * View application details
     */
    public function view($id)
    {
        $application = $this->applicationModel->find($id);
        
        if (!$application) {
            return $this->response->setJSON([
                'success' => false, 
                'message' => 'Application not found',
                'csrf_hash' => csrf_hash()
            ]);
        }

        $data = [
            'application' => $application
        ];

        return $this->response->setJSON([
            'success' => true,
            'html' => view('incoming_applicants/incoming_applicants_view_modal', $data),
            'csrf_hash' => csrf_hash()
        ]);
    }

    /**
     * Batch acknowledge multiple applications
     */
    public function batchAcknowledge()
    {
        $ids = $this->request->getPost('ids');
        
        if (empty($ids)) {
            return $this->response->setJSON([
                'success' => false, 
                'message' => 'No applications selected',
                'csrf_hash' => csrf_hash()
            ]);
        }
        
        $data = [
            'recieved_acknowledged' => date('Y-m-d H:i:s'),
            'recieved_by' => $this->session->get('user_id'),
            'recieved_at' => date('Y-m-d H:i:s'),
            'updated_by' => $this->session->get('user_id'),
            'status' => 'active'
        ];
        
        $successCount = 0;
        $failCount = 0;
        $emailSentCount = 0;
        
        foreach ($ids as $id) {
            try {
                if ($this->applicationModel->update($id, $data)) {
                    $successCount++;
                    
                    // Get the application details for email notification
                    $application = $this->applicationModel->find($id);
                    
                    // Send email notification
                    if ($application) {
                        $emailSent = $this->sendAcknowledgmentEmail($application);
                        if ($emailSent) $emailSentCount++;
                    }
                } else {
                    $failCount++;
                }
            } catch (\Exception $e) {
                log_message('error', 'Error in batch acknowledge for ID ' . $id . ': ' . $e->getMessage());
                $failCount++;
            }
        }
        
        return $this->response->setJSON([
            'success' => true,
            'message' => "$successCount applications acknowledged successfully. $failCount failed. $emailSentCount email notifications sent.",
            'csrf_hash' => csrf_hash()
        ]);
    }
} 