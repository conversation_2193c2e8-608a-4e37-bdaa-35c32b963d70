<?php

namespace Config;

// Create a new instance of our RouteCollection class.
$routes = Services::routes();

/*
 * --------------------------------------------------------------------
 * Router Setup
 * --------------------------------------------------------------------
 */
$routes->setDefaultNamespace('App\Controllers');
$routes->setDefaultController('Home');
$routes->setDefaultMethod('index');
$routes->setTranslateURIDashes(false);
$routes->set404Override();
$routes->setAutoRoute(false);
//$autoRoutesImproved(true);
// The Auto Routing (Legacy) is very dangerous. It is easy to create vulnerable apps
// where controller filters or CSRF protection are bypassed.


// Disable auto-routing to prevent conflicts
$routes->setAutoRoute(false);

// If you don't want to define all routes, please use the Auto Routing (Improved).
// Set `$autoRoutesImproved` to true in `app/Config/Feature.php` and set the following to true.
// $routes->setAutoRoute(false);

/*
 * --------------------------------------------------------------------
 * Route Definitions
 * --------------------------------------------------------------------
 */

// Public Routes (No Authentication Required)
$routes->group('', ['priority' => 1], function ($routes) {
    // Home Routes
    $routes->get('/', 'Home::index');
    $routes->get('login', 'Home::loginForm');
    $routes->post('login', 'Home::processLogin');
    $routes->get('logout', 'Home::logout');
    $routes->get('about', 'Home::about');
    $routes->get('findme/(:any)', 'Home::findme/$1');
    $routes->post('gofindme', 'Home::gofindme');
    $routes->post('open_profile', 'Home::open_profile');

    // Public Applicant Routes - only include routes that don't need authentication
    $routes->group('applicant', function ($routes) {
        $routes->get('register', 'Home::registerForm');  // Display register page
        $routes->post('register', 'Home::processRegister'); // Process registration
        $routes->get('activate/(:any)', 'Home::activate/$1');
        $routes->get('login', 'Home::applicantLoginForm'); // Display login page
        $routes->post('login', 'Home::processApplicantLogin'); // Process login

        // Public Job Routes
        $routes->get('jobs', 'ApplicantJobsController::index');
        $routes->get('jobs/(:num)', 'ApplicantJobsController::view/$1');
        $routes->get('jobs/position/(:num)', 'ApplicantJobsController::position/$1');
    });

    // Public Dakoii Routes
    $routes->get('dakoii', 'Dakoii::index');
    $routes->get('dakoii/login', 'Dakoii::loginForm');
    $routes->post('dakoii/login', 'Dakoii::processLogin');
    $routes->get('dakoii/logout', 'Dakoii::adminLogout');

    // Public API Routes
    $routes->group('api', function ($routes) {
        $routes->post('get_provinces', 'Api::get_provinces');
        $routes->post('get_countries', 'Api::get_countries');
        $routes->post('get_districts', 'Api::get_districts');
        $routes->post('get_llgs', 'Api::get_llgs');
    });

    // Add routes for jobs
    $routes->get('jobs', 'Home::jobs');
    $routes->get('jobs/view/(:num)', 'Home::view/$1');
});

// Protected Applicant Routes (Requires Applicant Login)
// Set a lower priority so these routes are checked after the public routes
$routes->group('applicant', ['filter' => 'auth', 'filter_args' => ['applicant'], 'priority' => 0], function($routes) {
    // Dashboard & Profile
    $routes->get('dashboard', 'ApplicantController::dashboard');
    $routes->get('profile', 'ApplicantController::profile');
    $routes->get('applications', 'ApplicantJobsController::applications');

    // Job Application Routes
    $routes->post('jobs/apply/(:num)', 'ApplicantJobsController::apply/$1');
    $routes->get('jobs/download/(:any)', 'ApplicantJobsController::downloadFile/$1');

    // Profile Management Routes
    $routes->group('profile', function($routes) {
        // Basic Profile Updates
        $routes->post('upload-photo', 'ApplicantController::uploadPhoto');
        $routes->post('update-personal', 'ApplicantController::updatePersonal');
        $routes->post('update-documents', 'ApplicantController::updateDocuments');
        $routes->post('update-employment', 'ApplicantController::updateEmployment');
        $routes->post('update-family', 'ApplicantController::updateFamily');
        $routes->post('update-additional', 'ApplicantController::updateAdditional');
        $routes->post('change-password', 'ApplicantController::changePassword');

        // Experience Management
        $routes->post('add-experience', 'ApplicantController::addExperience');
        $routes->post('update-experience', 'ApplicantController::updateExperience');
        $routes->post('delete-experience/(:num)', 'ApplicantController::deleteExperience/$1');

        // Education Management
        $routes->post('add-education', 'ApplicantController::addEducation');
        $routes->post('update-education', 'ApplicantController::updateEducation');
        $routes->post('delete-education/(:num)', 'ApplicantController::deleteEducation/$1');

        // File Management
        $routes->post('upload-file', 'ApplicantController::uploadFile');
        $routes->post('delete-file/(:num)', 'ApplicantController::deleteFile/$1');
    });

    // Application Management
    $routes->get('application/(:num)', 'ApplicantController::viewApplication/$1');
});

// Protected Admin Routes (Requires Admin Login)
$routes->group('', ['filter' => 'auth', 'priority' => 0], function($routes) {
    // Admin Dashboard
    $routes->get('dashboard', 'AdminController::admin_dashboard');
    $routes->get('admin/dashboard', 'AdminController::admin_dashboard');

    // Organization Settings
    $routes->get('settings/organization', 'OrgSettings::org_settings');
    $routes->post('settings/organization/update', 'OrgSettings::update');

    // Exercise Management
    $routes->group('exercises', function ($routes) {
        $routes->get('/', 'ExercisesController::exercise_management');
        $routes->get('list', 'ExercisesController::list');
        $routes->post('create', 'ExercisesController::create');
        $routes->get('get/(:num)', 'ExercisesController::get/$1');
        $routes->post('update/(:num)', 'ExercisesController::update/$1');
        $routes->post('delete/(:num)', 'ExercisesController::delete/$1');
        $routes->post('change-status', 'ExercisesController::changeStatus');

        // Pre-screening criteria routes
        $routes->get('pre_screen_criteria/(:num)', 'ExercisesController::pre_screen_criteria/$1');
        $routes->post('save_criteria/(:num)', 'ExercisesController::save_criteria/$1');
        $routes->get('get_criteria/(:num)', 'ExercisesController::get_criteria/$1');
    });

    // Position Management
    $routes->group('positions', function($routes) {
        $routes->get('positions_exercises', 'PositionsController::positions_exercises');
        $routes->get('positions_groups/(:num)', 'PositionsController::positions_groups/$1');
        $routes->post('addPositionGroup', 'PositionsController::addPositionGroup');
        $routes->post('updatePositionGroup', 'PositionsController::updatePositionGroup');
        $routes->post('deletePositionGroup/(:num)', 'PositionsController::deletePositionGroup/$1');
        $routes->get('view_positions/(:num)', 'PositionsController::view_positions/$1');
        $routes->post('add', 'PositionsController::addPosition');
        $routes->post('update', 'PositionsController::updatePosition');
        $routes->post('delete/(:num)', 'PositionsController::deletePosition/$1');
    });

    // Application Profiling Routes
    $routes->group('profile_applications_exercise', ['namespace' => 'App\Controllers'], function($routes) {
        $routes->get('/', 'ProfileController::index'); // List exercises
        $routes->get('profile_exercise/(:num)', 'ProfileController::profilePositionGroups/$1'); // List position groups for an exercise
        $routes->get('profile_group/(:num)', 'ProfileController::profilePositions/$1'); // List positions for a group
        $routes->get('profile_position/(:num)', 'ProfileController::profileApplicants/$1'); // List applicants for a position
        $routes->get('profile_view_employee/(:num)', 'ProfileController::profileViewEmployee/$1'); // View employee profile
        $routes->post('profile_update_status/(:num)', 'ProfileController::profileUpdateStatus/$1'); // Update profile status
        $routes->post('generate_profile/(:num)', 'ProfileController::generate_profile/$1'); // Generate employee profile
    });

    // Application Pre-Screening Routes
    $routes->group('application_pre_screening', ['namespace' => 'App\Controllers'], function($routes) {
        $routes->get('/', 'ApplicationPreScreeningController::index');
        $routes->get('show/(:num)', 'ApplicationPreScreeningController::show/$1');
        $routes->post('save/(:num)', 'ApplicationPreScreeningController::save/$1');
        $routes->get('exercises', 'ApplicationPreScreeningController::exercises');
        $routes->get('exercise_applications/(:num)', 'ApplicationPreScreeningController::exerciseApplications/$1');
        $routes->get('position_applications/(:num)', 'ApplicationPreScreeningController::positionApplications/$1');
    });

    // Maintaining compatibility with existing routes/views
    $routes->group('applications_pre_screening_exercise', ['namespace' => 'App\Controllers'], function($routes) {
        $routes->get('/', 'ApplicationPreScreeningController::exercises');
        $routes->get('view/(:num)', 'ApplicationPreScreeningController::viewPositionGroups/$1');
        $routes->get('group/(:num)', 'ApplicationPreScreeningController::viewPositions/$1');
        $routes->get('position/(:num)', 'ApplicationPreScreeningController::positionApplications/$1');
    });
});

// Protected Dakoii Routes (Requires Admin Login)
$routes->group('dakoii', ['filter' => 'auth'], function ($routes) {
    // Dashboard
    $routes->get('dashboard', 'Dakoii::dashboard');

    // Exercise Management
    $routes->post('exercise/change-status/(:num)', 'Dakoii::exerciseChangeStatus/$1');

    // Organizations Management
    $routes->group('organization', function ($routes) {
        $routes->get('list', 'Dakoii::organizationList');
        $routes->get('create', 'Dakoii::organizationCreateForm');
        $routes->post('create', 'Dakoii::organizationStore');
        $routes->get('view/(:any)', 'Dakoii::organizationView/$1');
        $routes->get('edit/(:any)', 'Dakoii::organizationEditForm/$1');
        $routes->post('update', 'Dakoii::organizationUpdate');
        $routes->post('admin/update', 'Dakoii::organizationAdminUpdate');
        $routes->get('admin/create/(:any)', 'Dakoii::organizationAdminCreateForm/$1');
        $routes->post('admin/create', 'Dakoii::organizationAdminStore');
        $routes->post('update-license', 'Dakoii::organizationUpdateLicense');
    });

    // System Users Management
    $routes->get('system-user/create', 'Dakoii::systemUserCreateForm');
    $routes->post('system-user/create', 'Dakoii::systemUserStore');
    $routes->post('system-user/update', 'Dakoii::systemUserUpdate');

    // Location Management
    $routes->group('', function ($routes) {
        // Province Management
        $routes->group('province', function ($routes) {
            $routes->get('list', 'Dakoii::provinceList');
            $routes->post('create', 'Dakoii::provinceCreate');
            $routes->post('update', 'Dakoii::provinceUpdate');
            $routes->get('delete/(:num)', 'Dakoii::provinceDelete/$1');
            $routes->get('get/(:num)', 'Dakoii::provinceGet/$1');
        });

        // District Management
        $routes->group('district', function ($routes) {
            $routes->get('list/(:num)', 'Dakoii::districtList/$1');
            $routes->get('get-by-province/(:num)', 'Dakoii::districtGetByProvince/$1');
            $routes->post('create', 'Dakoii::districtCreate');
            $routes->post('update', 'Dakoii::districtUpdate');
            $routes->get('delete/(:num)', 'Dakoii::districtDelete/$1');
        });

        // LLG Management
        $routes->group('llg', function ($routes) {
            $routes->get('list/(:num)', 'Dakoii::llgList/$1');
            $routes->post('create', 'Dakoii::llgCreate');
            $routes->post('update', 'Dakoii::llgUpdate');
            $routes->get('delete/(:num)', 'Dakoii::llgDelete/$1');
        });

        // Ward Management
        $routes->group('ward', function ($routes) {
            $routes->get('list/(:num)', 'Dakoii::wardList/$1');
            $routes->post('create', 'Dakoii::wardCreate');
            $routes->post('update', 'Dakoii::wardUpdate');
            $routes->get('delete/(:num)', 'Dakoii::wardDelete/$1');
        });

        // Education Management
        $routes->group('education', function ($routes) {
            $routes->post('create', 'Dakoii::educationCreate');
            $routes->post('update', 'Dakoii::educationUpdate');
        });
    });

    // Application Profiling Routes
    $routes->group('profile_applications_exercise', ['namespace' => 'App\Controllers'], function($routes) {
        $routes->get('/', 'ProfileController::index'); // List exercises
        $routes->get('profile_exercise/(:num)', 'ProfileController::profilePositionGroups/$1'); // List position groups for an exercise
        $routes->get('profile_group/(:num)', 'ProfileController::profilePositions/$1'); // List positions for a group
        $routes->get('profile_position/(:num)', 'ProfileController::profileApplicants/$1'); // List applicants for a position
        $routes->get('profile_view_employee/(:num)', 'ProfileController::profileViewEmployee/$1'); // View employee profile
        $routes->post('profile_update_status/(:num)', 'ProfileController::profileUpdateStatus/$1'); // Update profile status
        $routes->post('generate_profile/(:num)', 'ProfileController::generate_profile/$1'); // Generate employee profile
    });
});

// Incoming Applications routes
$routes->get('incoming_applications', 'IncomingApplicantsController::index');
$routes->get('incoming_applicants/view/(:num)', 'IncomingApplicantsController::view/$1');
$routes->post('incoming_applicants/acknowledge/(:num)', 'IncomingApplicantsController::acknowledge/$1');
$routes->post('incoming_applicants/batch_acknowledge', 'IncomingApplicantsController::batchAcknowledge');

// Dakoii Rating Items routes (RESTful)
$routes->get('/dakoii/rating_items/list', 'DakoiiRatingsController::index');
$routes->get('/dakoii/rating_items/new', 'DakoiiRatingsController::new');
$routes->post('/dakoii/rating_items/create', 'DakoiiRatingsController::create');
$routes->get('/dakoii/rating_items/edit/(:num)', 'DakoiiRatingsController::edit/$1');
$routes->post('/dakoii/rating_items/update/(:num)', 'DakoiiRatingsController::update/$1');
$routes->get('/dakoii/rating_items/delete/(:num)', 'DakoiiRatingsController::delete/$1');

// Dakoii Rating Scores routes (RESTful)
$routes->get('/dakoii/rating_scores/new/(:num)', 'DakoiiRatingsController::newScore/$1');
$routes->post('/dakoii/rating_scores/create', 'DakoiiRatingsController::createScore');
$routes->get('/dakoii/rating_scores/edit/(:num)', 'DakoiiRatingsController::editScore/$1');
$routes->post('/dakoii/rating_scores/update/(:num)', 'DakoiiRatingsController::updateScore/$1');
$routes->get('/dakoii/rating_scores/delete/(:num)', 'DakoiiRatingsController::deleteScore/$1');

// Rating Routes (RESTful) - Protected by auth filter
$routes->group('rating', ['filter' => 'auth', 'except' => []], function($routes) {
    $routes->get('', 'RatingController::index');
    $routes->get('position-groups/(:num)', 'RatingController::positionGroups/$1');
    $routes->get('positions/(:num)', 'RatingController::positions/$1');
    $routes->get('applications/(:num)', 'RatingController::applications/$1');
    $routes->get('rate/(:num)', 'RatingController::rate/$1');
    $routes->post('save', 'RatingController::saveRating');
    $routes->get('view/(:num)', 'RatingController::viewRating/$1');
    $routes->post('generate_ai_analysis', 'RatingController::generate_ai_analysis');
});

/*
 * --------------------------------------------------------------------
 * Additional Routing
 * --------------------------------------------------------------------
 *
 * There will often be times that you need additional routing and you
 * need it to be able to override any defaults in this file. Environment
 * based routes is one such time. require() additional route files here
 * to make that happen.
 *
 * You will have access to the $routes object within that file without
 * needing to reload it.
 */
if (is_file(APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php')) {
    require APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php';
}
