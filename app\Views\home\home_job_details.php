<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= esc($position['designation']) ?> - GRASS</title>
    <link rel="icon" href="<?= base_url() ?>public/assets/system_img/favicon.ico" type="image/x-icon">

    <!-- Core CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+3:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            /* Primary colors from the PNG flag color scheme */
            --red: #F00F00;             /* Red from PNG flag */
            --red-dark: #D00D00;        /* Darker red for hover states */
            --yellow: #FFC20F;          /* Yellow/Gold from PNG flag */
            --yellow-dark: #E6B00E;     /* Darker yellow for hover states */
            --black: #000000;           /* Black from PNG flag */
            --gray: #BFC1C7;            /* Light gray from the palette */
            --gray-dark: #9FA1A7;       /* Darker gray for hover states */
            --white: #FFFFFF;           /* White from the palette */
            --text-primary: #000000;    /* Primary text color */
            --text-secondary: #333333;  /* Secondary text color */
            --text-light: #FFFFFF;      /* Light text color for dark backgrounds */
        }

        body {
            font-family: 'Source Sans 3', sans-serif;
            color: var(--text-primary);
            background-color: #f8f9fa;
            padding-top: 70px;
        }

        /* Navbar styles */
        .navbar {
            background-color: var(--black) !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
        }

        .nav-link:hover {
            color: var(--yellow) !important;
        }

        /* Card styles */
        .card {
            border: none;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            border-radius: 12px;
            margin-bottom: 20px;
        }

        .card-header {
            background-color: rgba(240, 15, 0, 0.05);
            border-bottom: 1px solid rgba(240, 15, 0, 0.1);
            font-weight: 600;
        }

        /* Button styles */
        .btn-primary {
            background-color: var(--red);
            border-color: var(--red);
            color: white;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background-color: var(--red-dark);
            border-color: var(--red-dark);
            transform: translateY(-2px);
        }

        /* Section styles */
        .section-title {
            color: var(--red);
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .detail-label {
            font-weight: 600;
            color: var(--text-secondary);
        }

        .detail-value {
            margin-bottom: 1rem;
        }

        /* Badge styles */
        .badge-classification {
            background-color: var(--yellow);
            color: var(--black);
        }

        /* Deadline banner */
        .deadline-banner {
            background-color: rgba(240, 15, 0, 0.1);
            border-left: 4px solid var(--red);
            padding: 1rem;
            margin-bottom: 1.5rem;
        }
    </style>
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar navbar-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="<?= base_url() ?>">
                <img src="<?= base_url() ?>/public/assets/system_img/system-logo.png" alt="GRASS Logo" style="height: 48px;">
                <span class="d-none d-md-inline">GRASS</span>
            </a>
            <div>
                <a href="<?= base_url() ?>" class="nav-link d-inline-block me-3">Home</a>
                <a href="<?= base_url('jobs') ?>" class="nav-link d-inline-block">Jobs</a>
            </div>
        </div>
    </nav>

    <!-- Content -->
    <div class="container py-4">
        <!-- Back button -->
        <div class="mb-4">
            <a href="<?= base_url('jobs') ?>" class="btn btn-outline-primary">
                <i class="bi bi-arrow-left"></i> Back to Jobs
            </a>
        </div>

        <!-- Position Header -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h1 class="h2 mb-2"><?= esc($position['designation']) ?></h1>
                        <p class="mb-2">
                            <span class="badge badge-classification"><?= esc($position['classification']) ?></span>
                            <span class="text-muted ms-2">Reference: <?= esc($position['position_reference']) ?></span>
                        </p>
                        <p class="mb-0">
                            <strong><i class="bi bi-building"></i> Organization:</strong> <?= esc($position['org_name']) ?>
                        </p>
                        <p class="mb-0">
                            <strong><i class="bi bi-geo-alt"></i> Location:</strong> <?= esc($position['location']) ?>
                        </p>
                        <p class="mb-0">
                            <strong><i class="bi bi-cash"></i> Salary:</strong> <?= esc($position['annual_salary']) ?>
                        </p>
                    </div>
                    <div class="col-md-4">
                        <?php if (!empty($position['publish_date_to'])): ?>
                        <div class="deadline-banner">
                            <h5 class="mb-2">Application Deadline</h5>
                            <p class="mb-0 fw-bold"><?= date('d F Y', strtotime($position['publish_date_to'])) ?></p>
                            <small class="text-muted">Applications close at 11:59 PM</small>
                        </div>
                        <?php endif; ?>
                        <?php if (!empty($position['jd_filepath'])): ?>
                            <a href="<?= base_url(str_replace('public/', '', $position['jd_filepath'])) ?>" class="btn btn-primary w-100" target="_blank">
                                <i class="bi bi-file-earmark-pdf"></i> Download Job Description
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Position Details -->
        <div class="row">
            <!-- Left Column -->
            <div class="col-lg-8">
                <!-- Qualifications -->
                <?php if (!empty($position['qualifications'])): ?>
                <div class="card mb-4">
                    <div class="card-header">Qualifications</div>
                    <div class="card-body">
                        <?= nl2br(esc($position['qualifications'])) ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Knowledge -->
                <?php if (!empty($position['knowledge'])): ?>
                <div class="card mb-4">
                    <div class="card-header">Knowledge</div>
                    <div class="card-body">
                        <?= nl2br(esc($position['knowledge'])) ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Skills & Competencies -->
                <?php if (!empty($position['skills_competencies'])): ?>
                <div class="card mb-4">
                    <div class="card-header">Skills & Competencies</div>
                    <div class="card-body">
                        <?= nl2br(esc($position['skills_competencies'])) ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Job Experiences -->
                <?php if (!empty($position['job_experiences'])): ?>
                <div class="card mb-4">
                    <div class="card-header">Required Experience</div>
                    <div class="card-body">
                        <?= nl2br(esc($position['job_experiences'])) ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Right Column -->
            <div class="col-lg-4">
                <!-- Exercise Information -->
                <?php if (!empty($position['exercise_name'])): ?>
                <div class="card mb-4">
                    <div class="card-header">Exercise Information</div>
                    <div class="card-body">
                        <p class="mb-2">
                            <span class="detail-label">Exercise Name:</span><br>
                            <?= esc($position['exercise_name']) ?>
                        </p>
                        <?php if (!empty($position['publish_date_from'])): ?>
                        <p class="mb-2">
                            <span class="detail-label">Published Date:</span><br>
                            <?= date('d F Y', strtotime($position['publish_date_from'])) ?>
                        </p>
                        <?php endif; ?>
                        <?php if (!empty($position['publish_date_to'])): ?>
                        <p class="mb-0">
                            <span class="detail-label">Closing Date:</span><br>
                            <?= date('d F Y', strtotime($position['publish_date_to'])) ?>
                        </p>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Organization Information -->
                <div class="card mb-4">
                    <div class="card-header">Organization Information</div>
                    <div class="card-body">
                        <p class="mb-2">
                            <span class="detail-label">Organization:</span><br>
                            <?= !empty($position['org_name']) ? esc($position['org_name']) : 'Not specified' ?>
                        </p>
                        <p class="mb-0">
                            <span class="detail-label">Organization Code:</span><br>
                            <?= !empty($position['orgcode']) ? esc($position['orgcode']) : 'Not specified' ?>
                        </p>
                    </div>
                </div>

                <!-- Apply Now Card -->
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h5 class="mb-3">Interested in this position?</h5>
                        <p class="mb-3">Create an account or login to apply for this position.</p>
                        <a href="<?= base_url('applicant/login') ?>" class="btn btn-primary w-100 mb-2">Login to Apply</a>
                        <a href="<?= base_url('applicant/register') ?>" class="btn btn-outline-primary w-100">Create Account</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">© <?= date('Y') ?> GRASS - Government Recruitment & Selection System</p>
                    <p class="small mb-0">Powered by Dakoii Systems (www.dakoiims.com) & Echad Consultancy Services</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <img src="<?= base_url() ?>/public/assets/system_img/dakoii-logo.png" alt="Dakoii Systems" height="30" class="me-3">
                    <img src="<?= base_url() ?>/public/assets/system_img/echad-logo.png" alt="Echad Consultancy" height="30">
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Bootstrap Icons -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css"></script>
</body>
</html>
