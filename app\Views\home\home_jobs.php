<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Available Positions - GRASS</title>
    <link rel="icon" href="<?= base_url() ?>public/assets/system_img/favicon.ico" type="image/x-icon">

    <!-- Core CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+3:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            /* Primary colors from the PNG flag color scheme */
            --red: #F00F00;             /* Red from PNG flag */
            --red-dark: #D00D00;        /* Darker red for hover states */
            --yellow: #FFC20F;          /* Yellow/Gold from PNG flag */
            --yellow-dark: #E6B00E;     /* Darker yellow for hover states */
            --black: #000000;           /* Black from PNG flag */
            --gray: #BFC1C7;            /* Light gray from the palette */
            --gray-dark: #9FA1A7;       /* Darker gray for hover states */
            --white: #FFFFFF;           /* White from the palette */
            --text-primary: #000000;    /* Primary text color */
            --text-secondary: #333333;  /* Secondary text color */
            --text-light: #FFFFFF;      /* Light text color for dark backgrounds */
        }

        body {
            font-family: 'Source Sans 3', sans-serif;
            color: var(--text-primary);
            background-color: #f8f9fa;
            padding-top: 70px;
        }

        /* Navbar styles */
        .navbar {
            background-color: var(--black) !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
        }

        .nav-link:hover {
            color: var(--yellow) !important;
        }

        /* Card and table styles */
        .card {
            border: none;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(240, 15, 0, 0.15);
        }

        .table th {
            font-weight: 600;
            color: var(--red);
        }

        /* Button styles */
        .btn-primary {
            background-color: var(--red);
            border-color: var(--red);
            color: white;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background-color: var(--red-dark);
            border-color: var(--red-dark);
            transform: translateY(-2px);
        }

        /* Form control styles */
        .form-select {
            border-radius: 8px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            padding: 0.8rem 1.2rem;
            transition: all 0.3s ease;
        }

        .form-select:focus {
            border-color: var(--red);
            box-shadow: 0 0 0 3px rgba(240, 15, 0, 0.2);
        }

        /* Table row styles */
        .position-row {
            transition: all 0.3s ease;
        }

        .position-row:hover {
            background-color: rgba(240, 15, 0, 0.05) !important;
        }

        .small.text-muted {
            color: var(--text-secondary) !important;
        }

        /* Heading styles */
        h2 {
            color: var(--red);
            font-weight: 600;
        }

        /* Sort styles */
        .sort {
            cursor: pointer;
            color: var(--red);
        }
        .sort:hover {
            text-decoration: underline;
            color: var(--red-dark);
        }
        .sort.asc:after {
            content: " ↑";
            color: var(--yellow);
        }
        .sort.desc:after {
            content: " ↓";
            color: var(--yellow);
        }
    </style>
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar navbar-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="<?= base_url() ?>">
                <img src="<?= base_url() ?>/public/assets/system_img/system-logo.png" alt="GRASS Logo" style="height: 48px;">
                <span class="d-none d-md-inline">GRASS</span>
            </a>
            <div>
                <a href="<?= base_url() ?>" class="nav-link d-inline-block me-3">Home</a>
                <a href="<?= base_url('jobs') ?>" class="nav-link d-inline-block">Jobs</a>
            </div>
        </div>
    </nav>

    <!-- Content -->
    <div class="container">
        <h2 class="mb-4">Available Positions</h2>

        <!-- Simple filter -->
        <div class="mb-4">
            <select class="form-select w-auto" onchange="filterTable(this.value)">
                <option value="">All Organizations</option>
                <?php foreach ($organizations as $org): ?>
                    <option value="<?= esc($org['name']) ?>"><?= esc($org['name']) ?></option>
                <?php endforeach; ?>
            </select>
        </div>

        <!-- Simple table -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Position</th>
                                <th>Organization</th>
                                <th>Location</th>
                                <th>Salary</th>
                                <th>Closing Date</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($positions)): ?>
                                <?php foreach ($positions as $position): ?>
                                    <tr class="position-row" data-org="<?= esc($position['org_name']) ?>">
                                        <td>
                                            <strong class="text-dark"><?= esc($position['designation']) ?></strong>
                                            <div class="small text-muted"><?= esc($position['position_reference']) ?></div>
                                        </td>
                                        <td>
                                            <span class="text-dark"><?= esc($position['org_name']) ?></span>
                                            <div class="small text-muted"><?= esc($position['orgcode']) ?></div>
                                        </td>
                                        <td><?= esc($position['location']) ?></td>
                                        <td><?= esc($position['annual_salary']) ?></td>
                                        <td>
                                            <?php if (!empty($position['publish_date_to'])): ?>
                                                <?= date('d M Y', strtotime($position['publish_date_to'])) ?>
                                            <?php else: ?>
                                                <span class="text-muted">Not specified</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="<?= base_url('jobs/view/' . $position['id']) ?>" class="btn btn-primary btn-sm">View</a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="6" class="text-center p-4">
                                        <div class="text-muted">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-clipboard-x mb-3" viewBox="0 0 16 16">
                                                <path d="M6.5 0A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0zm3 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5z"/>
                                                <path d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1A2.5 2.5 0 0 1 9.5 5h-3A2.5 2.5 0 0 1 4 2.5zm4 7.793 1.146-1.147a.5.5 0 1 1 .708.708L8.707 10l1.147 1.146a.5.5 0 0 1-.708.708L8 10.707l-1.146 1.147a.5.5 0 0 1-.708-.708L7.293 10 6.146 8.854a.5.5 0 1 1 .708-.708z"/>
                                            </svg>
                                            <h4 class="text-red">No Positions Available</h4>
                                            <p>Check back later for new government positions.</p>
                                        </div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Very simple filtering script -->
    <script>
    function filterTable(org) {
        const rows = document.querySelectorAll('.position-row');

        rows.forEach(row => {
            if (org === '' || row.getAttribute('data-org') === org) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }
    </script>

    <?php if (session()->has('swal_icon')): ?>
    <script>
        Swal.fire({
            icon: '<?= session()->getFlashdata('swal_icon') ?>',
            title: '<?= session()->getFlashdata('swal_title') ?>',
            text: '<?= session()->getFlashdata('swal_text') ?>',
            confirmButtonColor: '#F00F00'
        });
    </script>
    <?php endif; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>