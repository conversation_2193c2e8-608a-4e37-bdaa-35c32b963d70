<?php

namespace App\Controllers;

use App\Models\districtModel;
use App\Models\orgModel;
use App\Models\provinceModel;
use App\Models\usersModel;

class AdminController extends BaseController
{
    public $session;
    public $usersModel;
    public $orgModel;
    public $provinceModel;
    public $districtModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();

        $this->usersModel = new usersModel();
        $this->orgModel = new orgModel();
        $this->provinceModel = new provinceModel();
        $this->districtModel = new districtModel();
    }

    public function admin_dashboard()
    {
        // Check if user is logged in and has admin role
        if (!$this->session->get('logged_in') || $this->session->get('role') !== 'admin') {
            return redirect()->to(base_url())->with('error', 'Access denied. Admin privileges required.');
        }

        // Get current organization ID from session
        $org_id = $this->session->get('org_id');

        $data = [
            'title' => 'Admin Dashboard',
            'menu' => 'dashboard',
            'total_users' => $this->usersModel->where('org_id', $org_id)->countAllResults(),
            'total_organizations' => $this->orgModel->countAll(),
            'total_provinces' => $this->provinceModel->countAll(),
            'user_name' => $this->session->get('name'),
        ];

        return view('admin/admin_dashboard', $data);
    }
}