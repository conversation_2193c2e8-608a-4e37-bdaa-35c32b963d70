<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GRASS - Government Recruitment and Selection System</title>
  <link rel="icon" href="<?= base_url() ?>public/assets/system_img/favicon.ico" type="image/x-icon">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Source Sans Pro - clean, elegant, professional font -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Source+Sans+3:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <style>
    :root {
      /* Primary colors from the PNG flag color scheme */
      --red: #F00F00;             /* Red from PNG flag */
      --red-dark: #D00D00;        /* Darker red for hover states */
      --yellow: #FFC20F;          /* Yellow/Gold from PNG flag */
      --yellow-dark: #E6B00E;     /* Darker yellow for hover states */
      --black: #000000;           /* Black from PNG flag */
      --gray: #BFC1C7;            /* Light gray from the palette */
      --gray-dark: #9FA1A7;       /* Darker gray for hover states */
      --white: #FFFFFF;           /* White from the palette */

      /* UI colors */
      --text-primary: #000000;    /* Primary text color */
      --text-secondary: #333333;  /* Secondary text color */
      --text-light: #FFFFFF;      /* Light text color for dark backgrounds */
      --bg-light: #FFFFFF;        /* Light background */
      --bg-dark: #000000;         /* Dark background */
    }

    /* Optimize font loading behavior */
    @font-face {
      font-family: 'Source Sans 3';
      font-display: swap;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif;
      scroll-behavior: smooth;
      color: var(--text-primary);
      line-height: 1.6;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    html {
      scroll-behavior: smooth;
    }

    h1, h2, h3, h4, h5, h6 {
      font-family: 'Source Sans 3', sans-serif;
      font-weight: 600;
      line-height: 1.3;
      color: var(--red);
    }

    /* Fix for dark text on dark background */
    .gradient-bg h1,
    .gradient-bg h2,
    .gradient-bg h3,
    .gradient-bg h4,
    .gradient-bg h5,
    .gradient-bg h6,
    .gradient-bg .text-white-75 {
      color: var(--white) !important;
    }

    p {
      font-weight: 400;
      margin-bottom: 1.5rem;
    }

    .gradient-bg {
      background: linear-gradient(135deg, var(--red), var(--red-dark));
      position: relative;
      overflow: hidden;
    }

    .gradient-bg::after {
      content: '';
      position: absolute;
      bottom: -50px;
      left: 0;
      width: 100%;
      height: 100px;
      background: var(--yellow);
      transform: skewY(-2deg);
      z-index: 1;
    }

    .content-wrapper {
      position: relative;
      z-index: 2;
      padding: 3rem 0;
    }

    .job-card {
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
      border-radius: 8px;
      box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
      border: none;
    }

    .job-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: linear-gradient(to bottom, var(--yellow), var(--yellow-dark));
      border-radius: 4px 0 0 4px;
    }

    .category-button {
      padding: 0.5rem 1.5rem;
      border-radius: 9999px;
      border: 2px solid var(--red);
      color: var(--red);
      transition: all 0.3s ease;
      font-weight: 500;
      font-size: 0.95rem;
    }

    .category-button:hover {
      background-color: var(--red);
      color: var(--white);
      transform: translateY(-2px);
    }

    .stat-card {
      background-color: white;
      padding: 2rem;
      border-radius: 12px;
      box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.08);
      transform: translateY(0);
      transition: all 0.3s ease;
      border: none;
    }

    .stat-card:hover {
      transform: translateY(-0.35rem);
      box-shadow: 0 15px 30px -5px rgba(0, 0, 0, 0.1);
    }

    .accent-gradient {
      background: linear-gradient(135deg, var(--red), var(--red-dark));
    }

    .hover-card {
      transition: all 0.3s ease;
      border-left: 4px solid transparent;
    }

    .hover-card:hover {
      transform: translateY(-5px);
      border-left-color: var(--yellow);
      box-shadow: 0 10px 20px rgba(240, 15, 0, 0.1);
    }

    .wave-shape {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 50px;
      background: url("data:image/svg+xml,...") center/cover no-repeat;
    }

    .featured-tag {
      background: linear-gradient(135deg, var(--red), var(--red-dark));
      padding: 2px 12px;
      border-radius: 999px;
      color: white;
      font-size: 0.875rem;
    }

    .bg-red {
      background-color: var(--red) !important;
    }

    .text-red {
      color: var(--red) !important;
    }

    .bg-yellow {
      background-color: var(--yellow) !important;
    }

    .text-yellow {
      color: var(--yellow) !important;
    }

    .bg-gray {
      background-color: var(--gray) !important;
    }

    .text-gray {
      color: var(--gray) !important;
    }

    /* Legacy class names for compatibility */
    .bg-navy {
      background-color: var(--red) !important;
    }

    .text-navy {
      color: var(--red) !important;
    }

    .bg-accent-red {
      background-color: var(--yellow) !important;
    }

    .text-accent-red {
      color: var(--yellow) !important;
    }

    .bg-orange-rust {
      background-color: var(--red) !important;
    }

    .text-orange-rust {
      color: var(--red) !important;
    }

    .bg-green {
      background-color: var(--yellow) !important;
    }

    .text-green {
      color: var(--yellow) !important;
    }

    .btn {
      font-weight: 500;
      letter-spacing: 0.02em;
      padding: 0.6rem 1.5rem;
      transition: all 0.3s ease;
    }

    .btn-red {
      background-color: var(--red);
      color: white;
      border: none;
      box-shadow: 0 4px 12px rgba(240, 15, 0, 0.2);
    }

    .btn-red:hover {
      background-color: var(--red-dark);
      color: white;
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(240, 15, 0, 0.3);
    }

    .btn-yellow {
      background-color: var(--yellow);
      color: var(--black);
      border: none;
      box-shadow: 0 4px 12px rgba(255, 194, 15, 0.2);
    }

    .btn-yellow:hover {
      background-color: var(--yellow-dark);
      color: var(--black);
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(255, 194, 15, 0.3);
    }

    .btn-black {
      background-color: var(--black);
      color: white;
      border: none;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .btn-black:hover {
      background-color: #333333;
      color: white;
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
    }

    /* Legacy button classes for compatibility */
    .btn-orange-rust {
      background-color: var(--red);
      color: white;
      border: none;
      box-shadow: 0 4px 12px rgba(240, 15, 0, 0.2);
    }

    .btn-orange-rust:hover {
      background-color: var(--red-dark);
      color: white;
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(240, 15, 0, 0.3);
    }

    .btn-green {
      background-color: var(--yellow);
      color: var(--black);
      border: none;
      box-shadow: 0 4px 12px rgba(255, 194, 15, 0.2);
    }

    .btn-green:hover {
      background-color: var(--yellow-dark);
      color: var(--black);
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(255, 194, 15, 0.3);
    }

    .btn-accent-red {
      background-color: var(--yellow);
      color: var(--black);
      border: none;
      box-shadow: 0 4px 12px rgba(255, 194, 15, 0.2);
    }

    .btn-accent-red:hover {
      background-color: var(--yellow-dark);
      color: var(--black);
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(255, 194, 15, 0.3);
    }

    .border-accent-red {
      border-color: var(--yellow) !important;
    }

    .border-navy {
      border-color: var(--red) !important;
    }

    .navbar {
      background-color: var(--black) !important;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      padding: 0.8rem 0;
    }

    .nav-link {
      color: rgba(255, 255, 255, 0.9) !important;
      transition: all 0.3s ease;
      padding: 0.6rem 1rem;
      font-weight: 500;
      position: relative;
    }

    .nav-link:hover {
      color: var(--yellow) !important;
    }

    .nav-link::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 0;
      height: 2px;
      background: var(--red);
      transition: all 0.3s ease;
      transform: translateX(-50%);
      opacity: 0;
    }

    .nav-link:hover::after {
      width: 70%;
      opacity: 1;
    }

    .content-wrapper {
      position: relative;
      z-index: 2;
      padding: 3rem 0;
    }

    .display-4 {
      font-weight: 700;
      letter-spacing: -0.02em;
      margin-bottom: 1.5rem;
    }

    .fs-4, .fs-5 {
      font-weight: 400;
      opacity: 0.9;
    }

    input.form-control {
      padding: 0.8rem 1.2rem;
      font-size: 1rem;
      border-radius: 8px;
      border: 1px solid rgba(0, 0, 0, 0.1);
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    input.form-control:focus {
      border-color: var(--red);
      box-shadow: 0 0 0 3px rgba(240, 15, 0, 0.2);
    }

    .position-card {
      transition: all 0.3s ease;
      border: none;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .position-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(240, 15, 0, 0.15);
      border-top: 3px solid var(--red);
    }

    .position-card .card-body {
      padding: 1.5rem;
    }

    .position-meta {
      font-size: 0.875rem;
      color: var(--text-secondary);
    }

    .position-deadline {
      font-size: 0.875rem;
      color: var(--red);
      font-weight: 500;
    }

    .view-all-link {
      color: var(--red);
      text-decoration: none;
      font-weight: 500;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      transition: all 0.3s ease;
    }

    .view-all-link:hover {
      color: var(--yellow);
      transform: translateX(5px);
    }
  </style>
</head>

<body class="bg-light">
  <!-- Navigation Menu -->
  <nav class="navbar navbar-dark navbar-expand-lg fixed-top">
    <div class="container d-flex justify-content-between align-items-center py-2">
      <div class="h4 mb-0 fw-bold d-flex align-items-center gap-3">
        <img src="<?= base_url() ?>/public/assets/system_img/system-logo.png" alt="GRASS Logo" style="height: 48px;">
        <span class="d-none d-md-inline text-white">GRASS</span>
      </div>
      <div class="d-flex gap-3">
        <a href="#home" class="nav-link">Home</a>
        <a href="<?= base_url('jobs') ?>" class="nav-link">Jobs</a>
        <a href="#apply" class="nav-link">Apply</a>
        <a href="#login" class="nav-link">Admin</a>
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <section id="home" class="gradient-bg text-white pt-5 pb-5" style="margin-top: 76px;">
    <div class="content-wrapper container text-center">
      <h1 class="display-4 fw-bold mb-4">Find Your Next Government Position</h1>
      <p class="fs-4 mb-3 text-white-75">Explore opportunities across various government departments</p>
      <p class="fs-5 mb-5 text-white-75">Your gateway to a rewarding career in public service</p>
    </div>
  </section>

  <!-- Featured Positions Section -->
  <section class="py-5 bg-white">
    <div class="container">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="h3 fw-bold text-navy mb-0">Featured Government Positions</h2>
        <a href="<?= base_url('jobs') ?>" class="btn btn-outline-danger">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-briefcase me-2" viewBox="0 0 16 16">
            <path d="M6.5 1A1.5 1.5 0 0 0 5 2.5V3H1.5A1.5 1.5 0 0 0 0 4.5v8A1.5 1.5 0 0 0 1.5 14h13a1.5 1.5 0 0 0 1.5-1.5v-8A1.5 1.5 0 0 0 14.5 3H11v-.5A1.5 1.5 0 0 0 9.5 1zm0 1h3a.5.5 0 0 1 .5.5V3H6v-.5a.5.5 0 0 1 .5-.5m1.886 6.914L15 7.151V12.5a.5.5 0 0 1-.5.5h-13a.5.5 0 0 1-.5-.5V7.15l6.614 1.764a1.5 1.5 0 0 0 .772 0M1.5 4h13a.5.5 0 0 1 .5.5v1.616L8.129 7.948a.5.5 0 0 1-.258 0L1 6.116V4.5a.5.5 0 0 1 .5-.5"/>
          </svg>
          View All Positions
        </a>
      </div>

      <?php if (empty($latest_positions)): ?>
        <div class="card">
          <div class="card-body text-center py-5">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor" class="bi bi-clipboard-x text-muted mb-3" viewBox="0 0 16 16">
              <path d="M6.5 0A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0zm3 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5z"/>
              <path d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1A2.5 2.5 0 0 1 9.5 5h-3A2.5 2.5 0 0 1 4 2.5zm4 7.793 1.146-1.147a.5.5 0 1 1 .708.708L8.707 10l1.147 1.146a.5.5 0 0 1-.708.708L8 10.707l-1.146 1.147a.5.5 0 0 1-.708-.708L7.293 10 6.146 8.854a.5.5 0 1 1 .708-.708z"/>
            </svg>
            <h3 class="h4 text-muted mb-2">No Positions Available</h3>
            <p class="text-muted mb-4">Check back later for new government positions.</p>
            <a href="<?= base_url('jobs') ?>" class="btn btn-red">
              Browse All Positions
            </a>
          </div>
        </div>
      <?php else: ?>
        <div class="row g-4">
          <?php foreach ($latest_positions as $position): ?>
            <div class="col-md-6 col-lg-4">
              <div class="position-card card h-100">
                <div class="card-body">
                  <div class="d-flex justify-content-between align-items-start mb-3">
                    <h3 class="h5 fw-bold text-red mb-0"><?= esc($position['designation']) ?></h3>
                    <span class="badge bg-yellow"><?= esc($position['classification']) ?></span>
                  </div>
                  <div class="position-meta mb-2">
                    <div class="mb-1">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-building me-2" viewBox="0 0 16 16">
                        <path d="M4 2.5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 .5.5v10.5a.5.5 0 0 1-.5.5h-7a.5.5 0 0 1-.5-.5V2.5zm1 0v10h6V2.5H5z"/>
                        <path d="M2 2a2 2 0 0 0-2 2v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H2zm13 2v11H1V4a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1z"/>
                      </svg>
                      <?= esc($position['org_name']) ?>
                    </div>
                    <div class="mb-1">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-geo-alt me-2" viewBox="0 0 16 16">
                        <path d="M12.166 8.94c-.524 1.062-1.234 2.12-1.96 3.07A31.493 31.493 0 0 1 8 14.58a31.481 31.481 0 0 1-2.206-2.57c-.726-.95-1.436-2.008-1.96-3.07C3.304 7.867 3 6.862 3 6a5 5 0 0 1 10 0c0 .862-.305 1.867-.834 2.94zM8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10z"/>
                        <path d="M8 8a2 2 0 1 1 0-4 2 2 0 0 1 0 4zm0 1a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/>
                      </svg>
                      <?= esc($position['location']) ?>
                    </div>
                    <div>
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-cash me-2" viewBox="0 0 16 16">
                        <path d="M8 10a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"/>
                        <path d="M0 4a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H1a1 1 0 0 1-1-1V4zm3 0a2 2 0 0 1-2 2v4a2 2 0 0 1 2 2h10a2 2 0 0 1 2-2V6a2 2 0 0 1-2-2H3z"/>
                      </svg>
                      <?= esc($position['annual_salary']) ?>
                    </div>
                  </div>
                  <div class="position-deadline mb-3">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-calendar-event me-2" viewBox="0 0 16 16">
                      <path d="M11 6.5a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1z"/>
                      <path d="M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM1 4v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V4H1z"/>
                    </svg>
                    Closes on <?= date('d M Y', strtotime($position['publish_date_to'])) ?>
                  </div>
                  <a href="<?= base_url('jobs/view/' . $position['id']) ?>" class="btn btn-red w-100">View Details</a>
                </div>
              </div>
            </div>
          <?php endforeach; ?>
        </div>
      <?php endif; ?>
    </div>
  </section>

  <!-- Statistics Section -->
  <section class="py-5 bg-light">
    <div class="container">
      <div class="row g-4">
        <div class="col-md-4">
          <div class="stat-card">
            <div class="display-5 fw-bold text-red mb-2">2,500+</div>
            <div class="text-muted">Government Positions</div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="stat-card">
            <div class="display-5 fw-bold text-yellow mb-2">100+</div>
            <div class="text-muted">Government Agencies</div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="stat-card">
            <div class="display-5 fw-bold text-gray mb-2">5,000+</div>
            <div class="text-muted">Active Applicants</div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section id="features" class="py-5 bg-light">
    <div class="container">
      <h2 class="display-5 fw-bold text-center mb-3">System Features</h2>
      <p class="text-center text-muted mb-5 mx-auto" style="max-width: 700px;">Discover the powerful tools and features designed to make your public service experience seamless.</p>

      <div class="row g-4">
        <div class="col-md-4">
          <div class="card h-100 job-card">
            <div class="card-body p-4">
              <h3 class="h5 fw-bold mb-3 text-navy">Easy Application Process</h3>
              <p class="text-muted">Submit your applications with just a few clicks. Our streamlined process makes job hunting effortless.</p>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card h-100 job-card">
            <div class="card-body p-4">
              <h3 class="h5 fw-bold mb-3 text-navy">Real-time Updates</h3>
              <p class="text-muted">Stay informed with instant notifications about your application status and new job postings.</p>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card h-100 job-card">
            <div class="card-body p-4">
              <h3 class="h5 fw-bold mb-3 text-navy">Document Management</h3>
              <p class="text-muted">Securely store and manage your documents. Upload once, use anywhere within the system.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Apply Section -->
  <section id="apply" class="py-5 bg-light">
    <div class="container">
      <h2 class="display-5 fw-bold text-center mb-3 text-navy">Applicant Portal</h2>
      <p class="text-center text-muted mb-5">Login to apply for positions or create a new account</p>

      <?php if (session()->has('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
          <?= session()->getFlashdata('success') ?>
          <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
      <?php endif; ?>

      <?php if (session()->has('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
          <?= session()->getFlashdata('error') ?>
          <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
      <?php endif; ?>

      <?php if (session()->has('warning')): ?>
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
          <?= session()->getFlashdata('warning') ?>
          <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
      <?php endif; ?>

      <?php if (session()->has('errors')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
          <ul class="mb-0">
            <?php foreach (session()->getFlashdata('errors') as $error): ?>
              <li><?= $error ?></li>
            <?php endforeach; ?>
          </ul>
          <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
      <?php endif; ?>

      <div class="row g-4 justify-content-center" style="max-width: 1000px; margin: 0 auto;">
        <!-- Login Form -->
        <div class="col-md-6">
          <div class="card h-100 border-top border-4 border-red">
            <div class="card-body p-4">
              <h3 class="h4 fw-bold mb-4 text-red">Login</h3>
              <form action="<?= base_url('applicant/login') ?>" method="post">
                <?= csrf_field() ?>
                <div class="mb-3">
                  <input type="email" name="email" class="form-control" placeholder="Email address" required value="<?= old('email') ?>">
                </div>
                <div class="mb-4">
                  <input type="password" name="password" class="form-control" placeholder="Password" required>
                </div>
                <button type="submit" class="btn btn-red w-100">Login</button>
              </form>
            </div>
          </div>
        </div>

        <!-- Register Form -->
        <div class="col-md-6">
          <div class="card h-100 border-top border-4 border-yellow">
            <div class="card-body p-4">
              <h3 class="h4 fw-bold mb-4 text-yellow">Create Account</h3>
              <form action="<?= base_url('applicant/register') ?>" method="post">
                <?= csrf_field() ?>
                <div class="row g-3 mb-3">
                  <div class="col-md-6">
                    <input type="text" name="firstname" class="form-control <?= session()->has('errors.firstname') ? 'is-invalid' : '' ?>" placeholder="First name" required value="<?= old('firstname') ?>">
                    <?php if (session()->has('errors.firstname')): ?>
                      <div class="invalid-feedback"><?= session()->getFlashdata('errors')['firstname'] ?></div>
                    <?php endif; ?>
                  </div>
                  <div class="col-md-6">
                    <input type="text" name="lastname" class="form-control <?= session()->has('errors.lastname') ? 'is-invalid' : '' ?>" placeholder="Last name" required value="<?= old('lastname') ?>">
                    <?php if (session()->has('errors.lastname')): ?>
                      <div class="invalid-feedback"><?= session()->getFlashdata('errors')['lastname'] ?></div>
                    <?php endif; ?>
                  </div>
                </div>
                <div class="mb-3">
                  <input type="email" name="email" class="form-control <?= session()->has('errors.email') ? 'is-invalid' : '' ?>" placeholder="Email address" required value="<?= old('email') ?>">
                  <?php if (session()->has('errors.email')): ?>
                    <div class="invalid-feedback"><?= session()->getFlashdata('errors')['email'] ?></div>
                  <?php endif; ?>
                </div>
                <div class="mb-3">
                  <input type="password" name="password" class="form-control <?= session()->has('errors.password') ? 'is-invalid' : '' ?>" placeholder="Password" required>
                  <?php if (session()->has('errors.password')): ?>
                    <div class="invalid-feedback"><?= session()->getFlashdata('errors')['password'] ?></div>
                  <?php endif; ?>
                </div>
                <div class="mb-4">
                  <input type="password" name="confirm_password" class="form-control <?= session()->has('errors.confirm_password') ? 'is-invalid' : '' ?>" placeholder="Confirm password" required>
                  <?php if (session()->has('errors.confirm_password')): ?>
                    <div class="invalid-feedback"><?= session()->getFlashdata('errors')['confirm_password'] ?></div>
                  <?php endif; ?>
                </div>
                <button type="submit" class="btn btn-yellow w-100">Create Account</button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Admin Login Section -->
  <section id="login" class="py-5 gradient-bg">
    <div class="content-wrapper container text-center">
      <h2 class="display-5 fw-bold mb-4 text-white">Admin Portal</h2>
      <p class="fs-5 mb-5 text-white">Access the administrative dashboard</p>

      <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
          <div class="card">
            <div class="card-body p-4">
              <h3 class="h4 fw-bold mb-4 text-red">Admin Login</h3>
              <form action="<?= base_url('login') ?>" method="post">
                <?= csrf_field() ?>
                <div class="mb-3">
                  <input type="text" name="username" class="form-control" placeholder="Username" required>
                </div>
                <div class="mb-4">
                  <input type="password" name="password" class="form-control" placeholder="Password" required>
                </div>
                <button type="submit" class="btn btn-red w-100">Login</button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer Section -->
  <footer class="bg-black text-white py-5 mt-5">
    <div class="container">
      <div class="row align-items-center gy-4">
        <div class="col-md-4 text-center text-md-start">
          <a href="https://www.dakoiims.com" target="_blank" class="d-inline-block mb-3">
            <img src="<?= base_url() ?>/public/assets/system_img/dakoii-systems-logo.png" alt="Dakoii Systems Logo" class="img-fluid" style="max-height: 60px;">
          </a>
          <p class="small mb-0 text-white-50">Innovative software solutions for government and enterprise.</p>
        </div>
        <div class="col-md-4 text-center">
          <h5 class="text-yellow mb-3">Partners in Excellence</h5>
          <a href="#" class="d-inline-block mb-3">
            <img src="<?= base_url() ?>/public/assets/system_img/echad-logo.png" alt="Echad Consultancy Services Logo" class="img-fluid" style="max-height: 60px;">
          </a>
        </div>
        <div class="col-md-4 text-center text-md-end">
          <h5 class="text-yellow mb-3">Contact</h5>
          <p class="small mb-1"><i class="bi bi-envelope-fill me-2"></i> <EMAIL></p>
          <p class="small mb-3"><i class="bi bi-globe me-2"></i> www.dakoiims.com</p>
          <div class="d-flex justify-content-center justify-content-md-end gap-3">
            <a href="#" class="text-white hover-text-yellow"><i class="bi bi-facebook fs-5"></i></a>
            <a href="#" class="text-white hover-text-yellow"><i class="bi bi-twitter fs-5"></i></a>
            <a href="#" class="text-white hover-text-yellow"><i class="bi bi-linkedin fs-5"></i></a>
          </div>
        </div>
      </div>
      <hr class="my-4 border-secondary">
      <div class="row">
        <div class="col-12 text-center">
          <p class="mb-0 small">
            &copy; <?= date('Y') ?> Developed by
            <a href="https://www.dakoiims.com" class="text-yellow text-decoration-none" target="_blank"> <strong>Dakoii Systems</strong> </a>
            in collaboration with <strong>Echad Consultancy Services</strong>. <span class="align-right" >Powered by Dakoii Systems</span>
          </p>
        </div>
      </div>
    </div>
  </footer>

  <!-- Add Bootstrap Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">

  <style>
    .hover-text-yellow:hover {
      color: var(--yellow) !important;
      transition: color 0.3s ease;
    }
  </style>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

  <?php if (session()->has('swal_icon')): ?>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      Swal.fire({
        icon: '<?= session()->getFlashdata('swal_icon') ?>',
        title: '<?= session()->getFlashdata('swal_title') ?>',
        text: '<?= session()->getFlashdata('swal_text') ?>',
        confirmButtonColor: '#F00F00'
      });
    });
  </script>
  <?php endif; ?>
</body>

</html>
